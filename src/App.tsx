import { useState, useEffect } from "react";
import "./App.css";
import { AppService } from "./services/AppService";
import { ConversationStorage } from "./services/ConversationStorage";
import { GAME_MESSAGES } from "./utils/gameUtils";
import { playAudioWithFallback } from "./utils/audioUtils";
import { SimpleVoiceChat } from "./components/SimpleVoiceChat";

function App() {
  const [aiLoading, setAiLoading] = useState<boolean>(false);
  const [generatedCharacter, setGeneratedCharacter] = useState<string>("");
  const [gameStarted, setGameStarted] = useState<boolean>(false);
  const [initialMessage, setInitialMessage] = useState<string>("");
  const [, setCharacterError] = useState<string>(""); // characterError

  const appService = AppService.getInstance();
  const conversationStorage = ConversationStorage.getInstance();

  // Limpiar conversaciones al inicializar la aplicación
  useEffect(() => {
    conversationStorage.clearAllConversations();
    console.log("🧹 Conversaciones limpiadas al inicializar la aplicación");
  }, [conversationStorage]);

  // Configurar el callback de audio en el servicio
  useEffect(() => {
    appService.setAudioCallback((audioUrl: string) => {
      // Intentar reproducir automáticamente con callback de finalización
      setTimeout(() => {
        // Obtener el callback de finalización del servicio
        const audioFinishedCallback = appService.getAudioFinishedCallback();
        playAudioWithFallback(audioUrl, audioFinishedCallback);
      }, 100);
    });
  }, [appService]);

  useEffect(() => {
    appService.debugPresetConfiguration();
  }, []);

  // Nueva función que combina generación de personaje e inicio del juego
  const handleStartGameDirectly = async () => {
    setAiLoading(true);
    setCharacterError("");

    try {
      // Paso 1: Generar personaje
      // console.log('🎲 Generando personaje automáticamente...');
      const characterResponse = await appService.generateWithGenCharBot(
        GAME_MESSAGES.GENERATE_CHARACTER
      );

      const characterText =
        characterResponse.response ||
        characterResponse.output ||
        characterResponse.result ||
        characterResponse.text ||
        characterResponse.content;

      if (!characterText) {
        throw new Error("No se pudo generar el personaje");
      }

      setGeneratedCharacter(characterText);
      // console.log('✅ Personaje generado:', characterText);

      // Paso 2: Iniciar el juego inmediatamente
      // console.log('🚀 Iniciando juego automáticamente...');
      setGameStarted(true);

      const gameResponse = await appService.generateWithIaVsPlayer(
        GAME_MESSAGES.INITIAL_GAME_QUERY,
        characterText
      );

      const responseText =
        gameResponse.response ||
        gameResponse.output ||
        gameResponse.result ||
        gameResponse.text ||
        gameResponse.content ||
        "Respuesta no encontrada";

      setInitialMessage(responseText);
      // console.log('🎮 Juego iniciado con mensaje:', responseText);
    } catch (error) {
      console.error("❌ Error en inicio directo del juego:", error);
      setCharacterError(
        "Error al generar personaje o iniciar juego. Inténtalo de nuevo."
      );
      setGameStarted(false);
      setGeneratedCharacter("");
    } finally {
      setAiLoading(false);
    }
  };

  const handleResetGame = () => {
    setGeneratedCharacter("");
    setGameStarted(false);
    setInitialMessage("");
    setCharacterError("");
    // Limpiar todas las conversaciones del localStorage
    conversationStorage.clearAllConversations();
    console.log("🔄 Juego reiniciado y conversaciones limpiadas");
  };

  return (
    <>
      <div className="card">
        <div
          style={{
            marginTop: "30px",
            padding: "20px",
            border: "1px solid #ccc",
            borderRadius: "8px",
          }}
        >
          <h3>🎯 Juego de Adivinanza de Personajes</h3>

          {/* Botón principal: Iniciar Juego Directo */}
          {!gameStarted && (
            <div
              style={{
                marginBottom: "30px",
                padding: "20px",
                backgroundColor: "#e8f5e8",
                borderRadius: "12px",
                textAlign: "center",
                border: "2px solid #28a745",
              }}
            >
              <h4 style={{ color: "#155724", marginBottom: "15px" }}>
                🚀 Inicio Rápido
              </h4>
              <p style={{ color: "#155724", marginBottom: "20px" }}>
                ¡Comienza a jugar inmediatamente! Se generará un personaje
                automáticamente y comenzará el juego.
              </p>
              <button
                onClick={handleStartGameDirectly}
                disabled={aiLoading}
                style={{
                  padding: "15px 30px",
                  backgroundColor: aiLoading ? "#ccc" : "#28a745",
                  color: "white",
                  border: "none",
                  borderRadius: "8px",
                  cursor: aiLoading ? "not-allowed" : "pointer",
                  fontSize: "18px",
                  fontWeight: "bold",
                  boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
                }}
              >
                {aiLoading ? "Iniciando Juego..." : "🎮 INICIAR JUEGO"}
              </button>
            </div>
          )}

          {/* Chat de voz simplificado - maneja todo el historial */}
          <SimpleVoiceChat
            generatedCharacter={generatedCharacter}
            isGameStarted={gameStarted}
            initialMessage={initialMessage}
          />

          {/* Botón para reiniciar */}
          {(generatedCharacter || gameStarted) && (
            <div style={{ marginTop: "20px", textAlign: "center" }}>
              <button
                onClick={handleResetGame}
                style={{
                  padding: "8px 16px",
                  backgroundColor: "#6c757d",
                  color: "white",
                  border: "none",
                  borderRadius: "4px",
                  cursor: "pointer",
                }}
              >
                🔄 Reiniciar Juego
              </button>
            </div>
          )}
        </div>
      </div>
    </>
  );
}

export default App;
