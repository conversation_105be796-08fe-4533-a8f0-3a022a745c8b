import type {
  ISpeechRecognitionService,
  ConversationState,
  SpeechRecognitionResult,
  ConversationStateChange,
} from "./impl/ISpeechRecognitionService";
import { isAudioCurrentlyPlaying } from "../utils/audioUtils";
import { useConversationStore } from "../stores/conversationStore";

// Extender la interfaz Window para incluir webkitSpeechRecognition
declare global {
  interface Window {
    webkitSpeechRecognition: any;
    SpeechRecognition: any;
  }
}

export class SpeechRecognitionService implements ISpeechRecognitionService {
  private static instance: SpeechRecognitionService;
  private recognition: any = null;
  private isCurrentlyListening = false;

  // Callbacks
  private resultCallback?: (result: SpeechRecognitionResult) => void;
  private stateChangeCallback?: (change: ConversationStateChange) => void;
  private errorCallback?: (error: string) => void;

  // Configuración
  private language = "es-ES";
  private continuous = true;
  private interimResults = true;

  private constructor() {
    this.initializeRecognition();
    // Subscribe to store changes
    useConversationStore.subscribe(
      (state) => state.conversationState,
      (newState) => {
        this.handleStateChange(newState);
      }
    );
  }

  static getInstance(): SpeechRecognitionService {
    if (!SpeechRecognitionService.instance) {
      SpeechRecognitionService.instance = new SpeechRecognitionService();
    }
    return SpeechRecognitionService.instance;
  }

  private initializeRecognition(): void {
    if (!this.isSupported()) {
      console.warn("⚠️ Speech Recognition no está soportado en este navegador");
      return;
    }

    const SpeechRecognition =
      window.SpeechRecognition || window.webkitSpeechRecognition;
    this.recognition = new SpeechRecognition();

    this.recognition.lang = this.language;
    this.recognition.continuous = this.continuous;
    this.recognition.interimResults = this.interimResults;
    this.recognition.maxAlternatives = 1;

    this.setupEventListeners();
  }

  private setupEventListeners(): void {
    if (!this.recognition) return;

    this.recognition.onstart = () => {
      console.log("🎤 Reconocimiento de voz iniciado");
      this.isCurrentlyListening = true;
      useConversationStore.getState().setIsListening(true);
      this.setConversationState("listening");
    };

    this.recognition.onend = () => {
      console.log("🎤 Reconocimiento de voz finalizado");
      this.isCurrentlyListening = false;
      useConversationStore.getState().setIsListening(false);
      if (this.getConversationState() === "listening") {
        this.setConversationState("idle");
      }
    };

    this.recognition.onresult = (event: any) => {
      const result = event.results[event.results.length - 1];
      const transcript = result[0].transcript;
      const confidence = result[0].confidence;
      const isFinal = result.isFinal;

      console.log(
        `🎤 Resultado: "${transcript}" (confianza: ${confidence}, final: ${isFinal})`
      );

      if (this.resultCallback) {
        this.resultCallback({
          transcript,
          confidence,
          isFinal,
        });
      }
    };

    this.recognition.onerror = (event: any) => {
      console.error("❌ Error en reconocimiento de voz:", event.error);
      this.isCurrentlyListening = false;

      let errorMessage = "Error desconocido en el reconocimiento de voz";
      switch (event.error) {
        case "no-speech":
          errorMessage = "No se detectó habla";
          break;
        case "audio-capture":
          errorMessage = "No se pudo acceder al micrófono";
          break;
        case "not-allowed":
          errorMessage = "Permisos de micrófono denegados";
          break;
        case "network":
          errorMessage = "Error de red";
          break;
        default:
          errorMessage = `Error: ${event.error}`;
      }

      if (this.errorCallback) {
        this.errorCallback(errorMessage);
      }
    };
  }

  async startListening(): Promise<boolean> {
    if (!this.isSupported()) {
      console.error("❌ Speech Recognition no está soportado");
      return false;
    }

    // ✅ CORREGIDO: Verificar si hay audio reproduciéndose usando la función importada
    const store = useConversationStore.getState();
    if (store.smartMicrophoneEnabled) {
      if (isAudioCurrentlyPlaying()) {
        console.log("🔇 No se inicia el micrófono: hay audio reproduciéndose");
        return false;
      }

      if (store.conversationState === "speaking") {
        console.log(
          "🔇 No se inicia el micrófono: IA está en estado 'speaking'"
        );
        return false;
      }
    }

    if (this.isCurrentlyListening) {
      console.log("🎤 Ya está escuchando - no se reinicia");
      return true;
    }

    try {
      // ✅ NUEVO: Verificar estado del recognition antes de iniciar
      if (this.recognition && this.recognition.state === "listening") {
        console.log("🎤 Recognition ya está activo");
        return true;
      }

      // Reinicializar si es necesario
      if (!this.recognition) {
        this.initializeRecognition();
      }

      this.recognition.start();
      console.log("🎤 Iniciando reconocimiento de voz...");
      return true;
    } catch (error) {
      console.error("❌ Error iniciando reconocimiento:", error);

      // ✅ MEJORADO: Manejo más robusto de errores
      if (error instanceof Error) {
        if (error.message.includes("already started")) {
          console.log("🔄 Recognition ya iniciado, intentando reiniciar...");
          try {
            this.recognition.stop();
            await new Promise((resolve) => setTimeout(resolve, 200));
            this.recognition.start();
            return true;
          } catch (retryError) {
            console.error("❌ Error en reintento:", retryError);
          }
        }
      }

      if (this.errorCallback) {
        this.errorCallback("No se pudo iniciar el reconocimiento de voz");
      }
      return false;
    }
  }

  shouldBeListening(): boolean {
    const store = useConversationStore.getState();
    console.log("🔍 Evaluando shouldBeListening:", {
      storeResult: store.getShouldBeListening(),
      isSupported: this.isSupported(),
      audioPlaying: isAudioCurrentlyPlaying(),
      isCurrentlyListening: this.isCurrentlyListening,
    });

    if (!this.isSupported()) {
      console.log("❌ Speech recognition not supported");
      return false;
    }

    if (isAudioCurrentlyPlaying()) {
      console.log("❌ Audio is currently playing");
      return false;
    }

    // Use the store's computed shouldBeListening function
    const storeResult = store.getShouldBeListening();
    const result = storeResult && !this.isCurrentlyListening;

    if (!result) {
      console.log("❌ Should not be listening:", {
        storeResult: storeResult,
        isCurrentlyListening: this.isCurrentlyListening
      });
    } else {
      console.log("✅ Should be listening - all conditions met");
    }

    return result;
  }

  stopListening(): void {
    if (this.recognition && this.isCurrentlyListening) {
      this.recognition.stop();
    }
  }

  isListening(): boolean {
    return this.isCurrentlyListening;
  }

  getConversationState(): ConversationState {
    return useConversationStore.getState().conversationState;
  }

  setConversationState(state: ConversationState): void {
    const store = useConversationStore.getState();
    const previousState = store.conversationState;

    // Update the centralized store
    store.setConversationState(state);

    console.log(`🔄 Estado de conversación: ${previousState} → ${state}`);

    // Control inteligente del micrófono
    if (store.smartMicrophoneEnabled) {
      this.handleSmartMicrophoneControl(state, previousState);
    }

    if (this.stateChangeCallback) {
      this.stateChangeCallback({ state });
    }
  }

  // ✅ MEJORADO: Control más inteligente del micrófono
  private handleSmartMicrophoneControl(
    newState: ConversationState,
    previousState: ConversationState
  ): void {
    console.log(`🔄 Smart microphone control: ${previousState} → ${newState}`);

    switch (newState) {
      case "speaking":
        if (this.isCurrentlyListening) {
          console.log("🔇 Deteniendo micrófono: IA hablando");
          this.stopListening();
        }
        break;

      case "idle":
        if (previousState === "speaking") {
          console.log(
            "🎤 IA terminó de hablar, programando verificación de micrófono..."
          );

          setTimeout(() => {
            console.log(
              "🔍 Verificando condiciones para reactivar micrófono..."
            );

            if (this.shouldBeListening() && !this.isCurrentlyListening) {
              console.log(
                "🎤 ✅ Condiciones cumplidas - Reactivando micrófono"
              );
              this.startListening();
            } else {
              console.log("🎤 ❌ Condiciones no cumplidas para reactivar:", {
                shouldBeListening: this.shouldBeListening(),
                isListening: this.isCurrentlyListening,
                conversationState: this.getConversationState(),
                audioPlaying: isAudioCurrentlyPlaying(),
              });
            }
          }, 2500); // ✅ AUMENTADO: Más tiempo para que termine completamente
        }
        break;

      case "processing":
        if (this.isCurrentlyListening) {
          console.log("🔇 Deteniendo micrófono: procesando");
          this.stopListening();
        }
        break;
    }
  }

  onResult(callback: (result: SpeechRecognitionResult) => void): void {
    this.resultCallback = callback;
  }

  onStateChange(callback: (change: ConversationStateChange) => void): void {
    this.stateChangeCallback = callback;
  }

  onError(callback: (error: string) => void): void {
    this.errorCallback = callback;
  }

  enableSmartMicrophoneControl(): void {
    useConversationStore.getState().setSmartMicrophoneEnabled(true);
    console.log("✅ Control inteligente de micrófono activado");
  }

  disableSmartMicrophoneControl(): void {
    useConversationStore.getState().setSmartMicrophoneEnabled(false);
    console.log("❌ Control inteligente de micrófono desactivado");
  }

  private handleStateChange(newState: ConversationState): void {
    console.log(`🔄 [Service] Handling state change to: ${newState}`);
    // This method can be used for any service-specific logic when state changes
    // The actual state is already updated in the store
  }

  setLanguage(language: string): void {
    this.language = language;
    if (this.recognition) {
      this.recognition.lang = language;
    }
  }

  setContinuous(continuous: boolean): void {
    this.continuous = continuous;
    if (this.recognition) {
      this.recognition.continuous = continuous;
    }
  }

  setInterimResults(interimResults: boolean): void {
    this.interimResults = interimResults;
    if (this.recognition) {
      this.recognition.interimResults = interimResults;
    }
  }

  isSupported(): boolean {
    return !!(window.SpeechRecognition || window.webkitSpeechRecognition);
  }
}
