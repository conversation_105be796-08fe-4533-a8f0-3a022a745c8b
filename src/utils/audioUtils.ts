// audioUtils.ts - Versión corregida

export const playAudioWithFallback = async (
  audioUrl: string,
  onEnded?: () => void,
  onStarted?: () => void,
  onError?: (error: string) => void
): Promise<void> => {
  let callbackExecuted = false;
  let timeoutId: NodeJS.Timeout | number;
  let audio: HTMLAudioElement;
  let audioActuallyStarted = false; // ✅ NUEVO: Rastrear si el audio realmente empezó

  const executeCallback = (source: string) => {
    if (!callbackExecuted && onEnded) {
      callbackExecuted = true;
      console.log(`🎤 Callback ejecutado desde: ${source}`);
      clearTimeout(timeoutId);
      onEnded();
      (window as any).audioCallbackExecuted = true;
    }
  };

  try {
    audio = new Audio(audioUrl);
    audio.preload = 'auto';
    audio.volume = 0.8;

    (window as any).currentAudio = audio;
    (window as any).audioCallbackExecuted = false;

    // ✅ CORREGIDO: Solo ejecutar callback cuando el audio realmente termine
    audio.addEventListener('ended', () => {
      if (audioActuallyStarted) {
        console.log('🎤 Audio terminado (evento ended) - audio había empezado correctamente');
        executeCallback('ended-event');
      } else {
        console.log('⚠️ Audio terminado pero nunca empezó realmente');
      }
    });

    audio.addEventListener('error', (e) => {
      console.error('❌ Error reproduciendo audio:', e);
      executeCallback('error-fallback');
      if (onError) {
        onError(`Error de audio: ${e.type}`);
      }
    });

    // ✅ CORREGIDO: Marcar cuando el audio realmente empiece
    audio.addEventListener('playing', () => {
      // console.log('🔊 Audio empezó a sonar REALMENTE');
      audioActuallyStarted = true; // ✅ NUEVO: Marcar que el audio empezó
      if (onStarted) {
        onStarted();
      }
    });

    audio.addEventListener('loadedmetadata', () => {
      const duration = audio.duration || 10;
      const timeoutDuration = (duration + 3) * 1000;

      // console.log(`⏱️ Configurando timeout de ${timeoutDuration}ms para audio de ${duration}s`);

      timeoutId = setTimeout(() => {
        if (!callbackExecuted && audioActuallyStarted) { // ✅ NUEVO: Solo si el audio empezó
          console.log('⏰ Timeout alcanzado, ejecutando callback por seguridad');
          executeCallback('timeout-fallback');
        }
      }, timeoutDuration);
    });

    try {
      await audio.play();
      console.log('✅ Audio iniciado exitosamente');
    } catch (playError) {
      if (playError instanceof Error && playError.name === 'AbortError') {
        // console.warn('⚠️ Reproducción interrumpida (AbortError)');
        // ✅ CORREGIDO: NO ejecutar callback inmediatamente por AbortError
        // Esperar a ver si el audio realmente se reproduce
        // console.log('⏳ Esperando a ver si el audio se recupera del AbortError...');

        // Solo ejecutar callback si después de tiempo razonable no hay reproducción
        setTimeout(() => {
          if (!audioActuallyStarted && !callbackExecuted) {
            console.log('⏰ AbortError confirmado - audio nunca empezó');
            executeCallback('abort-confirmed');
          }
        }, 3000);
      } else {
        throw playError;
      }
    }

  } catch (error) {
    console.warn('⚠️ Error iniciando reproducción:', error);

    const fallbackTimeout = 3000;
    timeoutId = setTimeout(() => {
      if (!callbackExecuted) {
        console.log('⏰ Error timeout, ejecutando callback');
        executeCallback('error-timeout-fallback');
      }
    }, fallbackTimeout);

    if (onError) {
      onError(`Error de reproducción: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  }
};

// ✅ MEJORADO: Función para verificar si hay audio reproduciéndose
export const isAudioCurrentlyPlaying = (): boolean => {
  const currentAudio = (window as any).currentAudio as HTMLAudioElement;
  if (!currentAudio) return false;

  // Verificar múltiples condiciones
  const isPlaying = !currentAudio.paused &&
                   !currentAudio.ended &&
                   currentAudio.readyState > 2 &&
                   currentAudio.currentTime > 0;

  console.log('🔍 Estado del audio:', {
    exists: !!currentAudio,
    paused: currentAudio?.paused,
    ended: currentAudio?.ended,
    readyState: currentAudio?.readyState,
    currentTime: currentAudio?.currentTime,
    isPlaying
  });

  return isPlaying;
};

// ✅ NUEVO: Función para detener audio actual
export const stopCurrentAudio = (): void => {
  const currentAudio = (window as any).currentAudio as HTMLAudioElement;
  if (currentAudio) {
    currentAudio.pause();
    currentAudio.currentTime = 0;
    (window as any).currentAudio = null;
    (window as any).audioCallbackExecuted = true; // Prevenir callbacks
    console.log('🛑 Audio detenido manualmente');
  }
};

export const createAudioElement = (audioUrl: string, autoplay: boolean = true): HTMLAudioElement => {
  const audio = new Audio(audioUrl);
  audio.controls = true;
  audio.preload = 'auto';
  audio.volume = 0.8;

  if (autoplay) {
    // Intentar reproducir cuando esté listo
    audio.addEventListener('canplaythrough', () => {
      audio.play().catch(error => {
        console.warn('⚠️ Autoplay bloqueado:', error);
      });
    });
  }

  return audio;
};
